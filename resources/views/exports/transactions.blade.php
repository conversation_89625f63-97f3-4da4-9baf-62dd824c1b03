<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Transactions Export</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
        }
        
        .date-range {
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 10px;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Transactions Report</h1>
    </div>
    
    @if($startDate || $endDate)
        <div class="date-range">
            Period: 
            @if($startDate && $endDate)
                {{ \Carbon\Carbon::parse($startDate)->format('M d, Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('M d, Y') }}
            @elseif($startDate)
                From {{ \Carbon\Carbon::parse($startDate)->format('M d, Y') }}
            @elseif($endDate)
                Until {{ \Carbon\Carbon::parse($endDate)->format('M d, Y') }}
            @endif
        </div>
    @endif
    
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Reference</th>
                <th>Type</th>
                <th>Currency</th>
                <th>Debit</th>
                <th>Credit</th>
                <th>Bank</th>
                <th>Customer</th>
                <th>Created By</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @forelse($transactions as $transaction)
                <tr>
                    <td>{{ $transaction->created_at ? $transaction->created_at->format('Y-m-d H:i') : '' }}</td>
                    <td>
                        {{ $transaction->reference ?? 
                           ($transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable
                            ? $transaction->transactionable->reference
                            : ($transaction->currencyOrder ? $transaction->currencyOrder->reference : '')) }}
                    </td>
                    <td>{{ $transaction->transactionType ? $transaction->transactionType->name : '' }}</td>
                    <td>{{ $transaction->currency ? $transaction->currency->code : '' }}</td>
                    <td class="text-right">
                        @if($transaction->debit)
                            {{ $transaction->currency->code }} {{ number_format($transaction->debit, 3) }}
                        @endif
                    </td>
                    <td class="text-right">
                        @if($transaction->credit)
                            {{ $transaction->currency->code }} {{ number_format($transaction->credit, 3) }}
                        @endif
                    </td>
                    <td>
                        {{ $transaction->transactionable_type === 'App\Models\Bank' && $transaction->transactionable
                            ? $transaction->transactionable->name
                            : '' }}
                    </td>
                    <td>{{ $transaction->currencyOrder && $transaction->currencyOrder->customer ? $transaction->currencyOrder->customer->name : 'Internal Transfer' }}</td>
                    <td>{{ $transaction->createdBy ? $transaction->createdBy->name : '' }}</td>
                    <td>{{ $transaction->transactionStatus ? $transaction->transactionStatus->name : '' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">No transactions found for the selected criteria.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    
    <div class="footer">
        <p>Generated on {{ now()->format('M d, Y \a\t H:i') }} | Total Records: {{ $transactions->count() }}</p>
    </div>
</body>
</html> 