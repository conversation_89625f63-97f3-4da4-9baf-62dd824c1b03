<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Transactions" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <Card>
            <CardContent class="p-0">
                <div class="p-4">
                    <SearchFilter @reset="reset">
                        <div>
                            <Label class="mb-2 inline-block">Date Range:</Label>
                            <Popover>
                                <PopoverTrigger as-child>
                                    <UIButton variant="outline"
                                        :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value.start ? 'text-muted-foreground' : ''}`">
                                        <CalendarIcon class="mr-2 h-4 w-4" />
                                        {{ formattedDateResult }}
                                    </UIButton>
                                </PopoverTrigger>
                                <PopoverContent class="w-auto p-0">
                                    <RangeCalendar v-model="value" initial-focus :number-of-months="2"
                                        @update:model-value="handleDateChange" />
                                </PopoverContent>
                            </Popover>
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Reference:</Label>
                            <Input v-model="form.reference" type="text" name="reference" placeholder="Reference"
                                autocomplete="off" />
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Transaction Type:</Label>
                            <Select v-model="form.transaction_type" class="form-select mt-1 w-full">
                                <SelectTrigger>
                                    <SelectValue placeholder="Select Transaction Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem v-for="type in transactionTypes" :key="type.value"
                                            :value="type.value">
                                            {{ type.label }}
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Currency:</Label>
                            <Input v-model="form.currency" type="text" name="currency" placeholder="Currency"
                                autocomplete="off" />
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Bank:</Label>
                            <Input v-model="form.bank" type="text" name="bank" placeholder="Bank" autocomplete="off" />
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Customer:</Label>
                            <Input v-model="form.customer" type="text" name="customer" placeholder="Customer"
                                autocomplete="off" />
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Created By:</Label>
                            <Input v-model="form.created_by" type="text" name="created_by" placeholder="Created by"
                                autocomplete="off" />
                        </div>
                        <template #actions>
                            <Button type="button" label="Search" @click="search" class="cursor-pointer" />
                        </template>
                    </SearchFilter>
                </div>
            </CardContent>
        </Card>
        <div>
            <Separator class="my-5" />
        </div>
        <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
            <h1 class="text-2xl font-bold">Transactions</h1>
            <div v-if="canExportTransaction" class="flex items-center gap-3 flex-wrap">
                <form :action="route('transactions.export')" method="get" class="inline-flex" @submit.prevent="handleCSVExportSubmit">
                    <input v-if="value.start" type="hidden" name="start_date" :value="value.start.toString()">
                    <input v-if="value.end" type="hidden" name="end_date" :value="value.end.toString()">
                    <input v-if="form.reference" type="hidden" name="reference" :value="form.reference">
                    <input v-if="form.transaction_type" type="hidden" name="transaction_type"
                        :value="form.transaction_type">
                    <input v-if="form.currency" type="hidden" name="currency" :value="form.currency">
                    <input v-if="form.bank" type="hidden" name="bank" :value="form.bank">
                    <input v-if="form.customer" type="hidden" name="customer" :value="form.customer">
                    <input v-if="form.created_by" type="hidden" name="created_by" :value="form.created_by">
                    <Button 
                        type="submit" 
                        :label="isExportingCSV ? 'Exporting...' : 'Export CSV'" 
                        :disabled="isExportingCSV"
                        :loading="isExportingCSV"
                        class="cursor-pointer" 
                    />
                </form>
                <form :action="route('transactions.export-pdf')" method="get" class="inline-flex" @submit.prevent="handlePDFExportSubmit">
                    <input v-if="value.start" type="hidden" name="start_date" :value="value.start.toString()">
                    <input v-if="value.end" type="hidden" name="end_date" :value="value.end.toString()">
                    <input v-if="form.reference" type="hidden" name="reference" :value="form.reference">
                    <input v-if="form.transaction_type" type="hidden" name="transaction_type"
                        :value="form.transaction_type">
                    <input v-if="form.currency" type="hidden" name="currency" :value="form.currency">
                    <input v-if="form.bank" type="hidden" name="bank" :value="form.bank">
                    <input v-if="form.customer" type="hidden" name="customer" :value="form.customer">
                    <input v-if="form.created_by" type="hidden" name="created_by" :value="form.created_by">
                    <Button 
                        type="submit" 
                        :label="isExportingPDF ? 'Exporting...' : 'Export PDF'" 
                        :disabled="isExportingPDF"
                        :loading="isExportingPDF"
                        class="cursor-pointer" 
                    />
                </form>
            </div>
        </div>
        <div class="bg-white overflow-x-auto mb-6">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead class="cursor-pointer" @click="changeSort('created_at')">
                            <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                                :direction="form.direction" />
                        </TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Transaction Type</TableHead>
                        <TableHead>Currency</TableHead>
                        <TableHead>Debit</TableHead>
                        <TableHead>Credit</TableHead>
                        <TableHead>Bank</TableHead>
                        <TableHead>Account Balance</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Created By</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="(transaction, index) in transactionsData" :key="transaction.id"
                        :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                        <TableCell>{{ transaction.created_at }}</TableCell>
                        <TableCell>{{ transaction.reference }}</TableCell>
                        <TableCell>{{ transaction.transaction_type }}</TableCell>
                        <TableCell>
                            <div class="flex items-center space-x-2">
                                <img v-if="transaction.currency?.photo" :src="transaction.currency?.photo"
                                    alt="Currency" class="w-6 h-6 rounded-full" />
                                <span>{{ transaction.currency.code }}</span>
                            </div>
                        </TableCell>
                        <TableCell>{{ transaction.debit ? `${transaction.currency.code} ${formatNumber(transaction.debit)}` : '' }}
                        </TableCell>
                        <TableCell>{{ transaction.credit ? `${transaction.currency.code} ${formatNumber(transaction.credit)}` : '' }}
                        </TableCell>
                        <TableCell>{{ transaction.bank }}</TableCell>
                        <TableCell>{{ transaction.account_balance ? `${transaction.currency.code} ${formatNumber(transaction.account_balance)}` : '' }}</TableCell>
                        <TableCell>{{ transaction.customer }}</TableCell>
                        <TableCell>{{ transaction.created_by }}</TableCell>
                        <TableCell>
                            <Badge class="text-xs rounded-full min-w-max" :class="{
                                '!bg-[#009A15]': transaction.transaction_status === 'Completed',
                                '!bg-gray-500': transaction.transaction_status === 'Cancelled'
                            }">
                                {{ transaction.transaction_status }}
                            </Badge>
                        </TableCell>
                        <TableCell>
                            <Link :href="route('transactions.show', transaction.id)">
                            <Tooltip label="More Details">
                                <Info />
                            </Tooltip>
                            </Link>
                        </TableCell>
                    </TableRow>
                    <TableRow v-if="!transactionsData.length">
                        <TableCell class="text-center border-0" colspan="10">No transactions found.</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
            <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
                <ShowEntries v-model="form.perPage" />
                <Pagination class="flex justify-end" :data="paginationData" />
            </div>
        </div>
    </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
    TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { RangeCalendar } from '@/Components/ui/range-calendar'
import { Button as UIButton } from '@/Components/ui/button'
import { Input } from '@/Components/ui/input'
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'

export default {
    components: {
        Head,
        Link,
        Card,
        CardContent,
        SearchFilter,
        Label,
        Select,
        SelectContent,
        SelectGroup,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Separator,
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        Button,
        TextInput,
        SelectInput,
        SwitchInput,
        Table,
        TableBody,
        TableCell,
        TableHead,
        TableHeader,
        TableRow,
        SortableHeader,
        Avatar,
        AvatarFallback,
        AvatarImage,
        Icon,
        Badge,
        Pagination,
        ShowEntries,
        Info,
        Tooltip,
        Breadcrumb,
        CommandItem,
        Check,
        Popover,
        PopoverContent,
        PopoverTrigger,
        CalendarIcon,
        RangeCalendar,
        UIButton,
        Input,
    },
    layout: Layout,
    props: {
        filters: Object,
        sort: Object,
        transactions: Object,
        transactionTypes: {
            type: Array,
            default: () => []
        },
    },
    data() {
        const start_date = this.filters.start_date ? parseDate(this.filters.start_date) : null;
        const end_date = this.filters.end_date ? parseDate(this.filters.end_date) : null;

        return {
            defaultValues: {
                sort: 'created_at',
                direction: 'desc',
                perPage: 10,
                start_date: null,
                end_date: null,
                reference: '',
                transaction_type: '',
                currency: '',
                bank: '',
                customer: '',
                created_by: '',
            },
            form: {
                search: this.filters?.search,
                sort: this.sort?.field ?? 'created_at',
                direction: this.sort?.direction ?? 'desc',
                perPage: this.filters?.perPage?.toString() || '10',
                start_date: this.filters.start_date || null,
                end_date: this.filters.end_date || null,
                reference: this.filters.reference || '',
                transaction_type: this.filters.transaction_type || '',
                currency: this.filters.currency || '',
                bank: this.filters.bank || '',
                customer: this.filters.customer || '',
                created_by: this.filters.created_by || '',
            },
            errors: {},
            breadcrumbs: [
                { name: 'Dashboard', link: route('dashboard') },
                { name: 'Transactions', link: route('transactions'), is_active: true },
            ],
            value: {
                start: start_date,
                end: end_date
            },
            canExportTransaction: false,
            df: new DateFormatter('en-US', {
                dateStyle: 'medium',
            }),
            isExportingCSV: false,
            isExportingPDF: false,
        };
    },
    computed: {
        transactionsData() {
            return this.transactions?.data ?? [];
        },
        paginationData() {
            if (!this.transactions) return {
                links: [],
                current_page: 1,
                last_page: 1,
                from: 0,
                to: 0,
                total: 0
            };
            return this.transactions;
        },
        formattedDateResult() {
            if (!this.value.start) {
                return 'Select Date Range'
            }

            if (!this.value.end) {
                return this.df.format(this.value.start.toDate(getLocalTimeZone()))
            }

            return `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`
        },
    },
    watch: {
        'form.perPage': function (newValue) {
            // Only watch perPage changes
            const params = { ...this.form, perPage: newValue };
            this.performSearch(params);
        }
    },
    created() {
        this.checkPermissions();
    },
    methods: {
        formatNumber(value) {
            if (value === null || value === undefined) return '0.00';
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 3
            }).format(value);
        },
        reset() {
            this.form = {
                search: null,
                sort: this.defaultValues.sort,
                direction: this.defaultValues.direction,
                perPage: this.defaultValues.perPage.toString(),
                start_date: null,
                end_date: null,
                reference: '',
                transaction_type: '',
                currency: '',
                bank: '',
                customer: '',
                created_by: '',
            }
            this.value = {
                start: null,
                end: null
            }
            this.$inertia.visit('/transactions', {
                preserveScroll: true,
                replace: true
            });
        },
        search() {
            this.performSearch(this.form);
        },
        performSearch(params) {
            this.$inertia.get('/transactions', pickBy(params, value => value !== null), {
                preserveState: true,
                preserveScroll: true,
                only: ['transactions'],
                replace: true
            });
        },
        changeSort(field) {
            if (field === 'created_at') {
                this.form.direction = this.form.direction === 'asc' ? 'desc' : 'asc';
                this.form.sort = field;
                this.performSearch(this.form);
            }
        },
        handleDateChange(newValue) {
            this.value = newValue;
            if (newValue.start) {
                this.form.start_date = newValue.start.toString();
            } else {
                this.form.start_date = null;
            }

            if (newValue.end) {
                this.form.end_date = newValue.end.toString();
            } else {
                this.form.end_date = null;
            }
        },
        checkPermissions() {
            const userRoles = this.$page.props.auth.user.roles || [];

            // Check if user has permission to export transactions
            this.canExportTransaction = userRoles.some(role =>
                role.permissions && role.permissions.some(permission =>
                    permission.name === 'export transaction'
                )
            );
        },
        async handleCSVExportSubmit(e) {
            // Check if date range is selected
            if (!this.value.start && !this.value.end) {
                // Set default 3 months date range
                const endDate = new Date();
                const startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 3);
                
                // Format dates to match your expected format (YYYY-MM-DD)
                const formattedStartDate = startDate.toISOString().split('T')[0];
                const formattedEndDate = endDate.toISOString().split('T')[0];
                
                // Alert user about using default date range
                const useDefault = confirm('No date range selected. System will export last 3 months data by default. Do you want to continue?');

                if (!useDefault) {
                    return;
                }

                // Add date range to form data
                const startDateInput = document.createElement('input');
                startDateInput.type = 'hidden';
                startDateInput.name = 'start_date';
                startDateInput.value = formattedStartDate;
                e.target.appendChild(startDateInput);

                const endDateInput = document.createElement('input');
                endDateInput.type = 'hidden';
                endDateInput.name = 'end_date';
                endDateInput.value = formattedEndDate;
                e.target.appendChild(endDateInput);
            }

            try {
                this.isExportingCSV = true;
                const form = e.target;
                const formData = new FormData(form);
                const queryString = new URLSearchParams(formData).toString();
                
                const response = await fetch(`${form.action}?${queryString}`, {
                    method: 'GET',
                });
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'transactions-export.csv');
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('CSV Export failed:', error);
                alert('Export failed. Please try again.');
            } finally {
                this.isExportingCSV = false;
            }
        },
        async handlePDFExportSubmit(e) {
            // Check if date range is selected
            if (!this.value.start && !this.value.end) {
                // Set default 3 months date range
                const endDate = new Date();
                const startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 3);
                
                // Format dates to match your expected format (YYYY-MM-DD)
                const formattedStartDate = startDate.toISOString().split('T')[0];
                const formattedEndDate = endDate.toISOString().split('T')[0];
                
                // Alert user about using default date range
                const useDefault = confirm('No date range selected. System will export last 3 months data by default. Do you want to continue?');

                if (!useDefault) {
                    return;
                }

                // Add date range to form data
                const startDateInput = document.createElement('input');
                startDateInput.type = 'hidden';
                startDateInput.name = 'start_date';
                startDateInput.value = formattedStartDate;
                e.target.appendChild(startDateInput);

                const endDateInput = document.createElement('input');
                endDateInput.type = 'hidden';
                endDateInput.name = 'end_date';
                endDateInput.value = formattedEndDate;
                e.target.appendChild(endDateInput);
            }

            try {
                this.isExportingPDF = true;
                const form = e.target;
                const formData = new FormData(form);
                const queryString = new URLSearchParams(formData).toString();
                
                const response = await fetch(`${form.action}?${queryString}`, {
                    method: 'GET',
                });
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'transactions-export.pdf');
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('PDF Export failed:', error);
                alert('Export failed. Please try again.');
            } finally {
                this.isExportingPDF = false;
            }
        },
    },
};

function parseDate(dateString) {
    const [year, month, day] = dateString.split('-').map(Number);
    return new CalendarDate(year, month, day);
}
</script>