<?php

namespace App\Http\Controllers;

use App\Exports\TransactionsExport;
use App\Models\Bank;
use App\Models\CurrencyOrder;
use App\Models\Transaction;
use App\Models\TransactionType;
use App\Services\CurrencyOrderService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Facades\Excel;

class TransactionsController extends Controller
{
    private CurrencyOrderService $currencyOrderService;

    public function __construct(CurrencyOrderService $currencyOrderService)
    {
        $this->currencyOrderService = $currencyOrderService;
    }

    public function index()
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        $transactions = Transaction::query()
            ->with(['currency', 'transactionType', 'currencyOrder.customer', 'createdBy', 'transactionable'])
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('debit', 'like', "%{$search}%")
                        ->orWhere('credit', 'like', "%{$search}%");
                });
            })
            ->when(request('start_date'), function ($query, $date) {
                $query->whereDate('created_at', '>=', $date);
            })
            ->when(request('end_date'), function ($query, $date) {
                $query->whereDate('created_at', '<=', $date);
            })
            ->when(request('reference'), function ($query, $reference) {
                $query->whereHas('currencyOrder', function ($q) use ($reference) {
                    $q->where('reference', 'like', "%{$reference}%");
                });
            })
            ->when(request('transaction_type'), function ($query, $type) {
                $query->whereHas('transactionType', function ($q) use ($type) {
                    $q->where('value', $type);
                });
            })
            ->when(request('currency'), function ($query, $currency) {
                $query->whereHas('currency', function ($q) use ($currency) {
                    $q->where('code', 'like', "%{$currency}%")
                        ->orWhere('name', 'like', "%{$currency}%");
                });
            })
            ->when(request('bank'), function ($query, $bank) {
                $query->whereHasMorph('transactionable', [Bank::class], function ($q) use ($bank) {
                    $q->where('name', 'like', "%{$bank}%");
                });
            })
            ->when(request('customer'), function ($query, $customer) {
                $query->whereHas('currencyOrder.customer', function ($q) use ($customer) {
                    $q->where('name', 'like', "%{$customer}%");
                });
            })
            ->when(request('created_by'), function ($query, $createdBy) {
                $query->whereHas('createdBy', function ($q) use ($createdBy) {
                    $q->where('name', 'like', "%{$createdBy}%");
                });
            });

        if ($sort === 'created_at') {
            $transactions->orderBy('created_at', $direction)
                ->orderBy('transaction_type_id', $direction);
        } else {
            $transactions->orderBy($sort, $direction);
        }

        $transactions = $transactions->paginate($perPage);

        return inertia('Transactions/Index', [
            'filters' => request()->only([
                'search',
                'perPage',
                'start_date',
                'end_date',
                'reference',
                'transaction_type',
                'currency',
                'bank',
                'customer',
                'created_by',
            ]),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'transactionTypes' => TransactionType::select('value', 'name as label')->get(),
            'transactions' => $transactions->through(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'reference' => $transaction->reference ?? ($transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable
                        ? $transaction->transactionable->reference
                        : ($transaction->currencyOrder ? $transaction->currencyOrder->reference : '')),
                    'transaction_type' => $transaction->transactionType->name,
                    'currency' => [
                        'code' => $transaction->currency->code,
                        'name' => $transaction->currency->name,
                        'photo' => $transaction->currency->photo,
                    ],
                    'debit' => $transaction->debit,
                    'credit' => $transaction->credit,
                    'bank' => $transaction->transactionable_type === 'App\Models\Bank' && $transaction->transactionable
                        ? $transaction->transactionable->name
                        : null,
                    'account_balance' => $transaction->account_balance,
                    'customer' => $transaction->currencyOrder ? $transaction->currencyOrder->customer->name : 'Internal Transfer',
                    'created_by' => $transaction->createdBy->name,
                    'transaction_status' => $transaction->transactionStatus->name,
                    'created_at' => $transaction->created_at,
                ];
            }),
        ]);
    }

    public function store(): RedirectResponse
    {
        $transactionType = TransactionType::where('value', Request::input('transaction_type_id'))->first();
        $currencyOrder = CurrencyOrder::find(Request::get('initiator_currency_order_id'));

        $rules = [
            'transaction_type_id' => ['required', function ($attribute, $value, $fail) {
                if (! TransactionType::where('value', $value)->exists()) {
                    $fail('Invalid transaction type value.');
                }
            }],
            'amount' => [
                'required',
                'numeric',
                'gt:0',
                function ($attribute, $value, $fail) use ($currencyOrder) {
                    if (!$currencyOrder) {
                        $fail('Currency order not found.');
                        return;
                    }

                    // Get existing non-cancelled transactions
                    $existingTransactions = $currencyOrder->transactions()
                        ->whereHas('transactionStatus', function($query) {
                            $query->where('value', '!=', 'cancelled');
                        })
                        ->whereHas('transactionType', function ($query) {
                            $query->where('value', '!=', 'bank_charges');
                        })
                        ->get();

                    $fulfilledReceivable = round($existingTransactions->sum('debit'), 3);
                    $fulfilledPayable = round($existingTransactions->sum('credit'), 3);

                    $orderType = $currencyOrder->currencyOrderType->value;
                    $isOutCurrency = $currencyOrder->out_currency_id == Request::get('currency_id');

                    if (in_array($orderType, ['e', 'tpp']) || ($orderType === 'po' && $isOutCurrency)) {
                        $remainingPayable = round($currencyOrder->payable_amount - $fulfilledPayable, 3);
                        if (round($value, 3) > $remainingPayable) {
                            $fail("Amount cannot exceed remaining payable amount: {$remainingPayable}");
                        }
                    } elseif (in_array($orderType, ['r', 'tpr', 'com']) || ($orderType === 'po' && !$isOutCurrency)) {
                        $remainingReceivable = round($currencyOrder->receivable_amount - $fulfilledReceivable, 3);
                        if (round($value, 3) > $remainingReceivable) {
                            $fail("Amount cannot exceed remaining receivable amount: {$remainingReceivable}");
                        }
                    }
                }
            ],
            'currency_id' => ['required', 'exists:currencies,id'],
            'initiator_currency_order_id' => 'required|exists:currency_orders,id',
        ];

        if ($transactionType?->value === 'contra') {
            $rules['currency_order_id'] = ['required', 'exists:currency_orders,id'];
        } elseif ($transactionType?->value === 'account') {
            $rules['bank_id'] = ['required', 'exists:banks,id'];

            // Add bank_charges validation for specific currency order types
            if ($currencyOrder) {
                $orderType = $currencyOrder->currencyOrderType->value;
                $isOutCurrency = $currencyOrder->out_currency_id == Request::get('currency_id');

                if (in_array($orderType, ['e', 'tpp']) || ($orderType === 'po' && $isOutCurrency)) {
                    $rules['bank_charges'] = ['nullable', 'numeric', 'gt:0'];
                    $rules['amount'] = array_merge($rules['amount'], [
                        function ($attribute, $value, $fail) {
                            $bank = Bank::find(Request::get('bank_id'));
                            if (! $bank) {
                                $fail('Bank not found.');
                                return;
                            }

                            $totalDeduction = $value;
                            if (Request::filled('bank_charges')) {
                                $totalDeduction += Request::get('bank_charges');
                            }

                            if ($bank->total_balance < $totalDeduction) {
                                $fail("Insufficient bank balance. Required: {$totalDeduction}, Available: {$bank->total_balance}");
                            }
                        },
                    ]);
                }
            }
        }

        Request::validate($rules);

        try {
            DB::transaction(function () use ($transactionType) {
                $currencyOrder = CurrencyOrder::find(Request::get('initiator_currency_order_id'));

                // Create main transaction
                $transaction = $this->createTransaction($currencyOrder, $transactionType);

                // Handle contra transaction if needed
                if ($transactionType->value === 'contra' && Request::filled('currency_order_id')) {
                    $linkedCurrencyOrder = CurrencyOrder::findOrFail(Request::get('currency_order_id'));
                    $secondTransaction = $this->createTransaction($linkedCurrencyOrder, $transactionType, $currencyOrder);
                    $this->currencyOrderService->updateStatus($linkedCurrencyOrder, $secondTransaction);
                }

                // Update status for main transaction
                $this->currencyOrderService->updateStatus($currencyOrder, $transaction);

                // Handle bank charges separately after main transaction
                if ($transactionType->value === 'account' && Request::filled('bank_charges')) {
                    $bankChargesType = TransactionType::where('value', 'bank_charges')->firstOrFail();
                    $completedStatus = \App\Models\TransactionStatus::where('value', 'completed')->firstOrFail();

                    $bankChargesTransaction = new Transaction;
                    $bankChargesTransaction->credit = Request::get('bank_charges');
                    $bankChargesTransaction->currencyOrder()->associate($currencyOrder->id);
                    $bankChargesTransaction->currency()->associate(Request::get('currency_id'));
                    $bankChargesTransaction->transactionType()->associate($bankChargesType);
                    $bankChargesTransaction->createdBy()->associate(Auth::user()->id);
                    $bankChargesTransaction->transaction_status_id = $completedStatus->id;

                    // Associate bank and calculate balance
                    $bank = Bank::findOrFail(Request::get('bank_id'));
                    $bankChargesTransaction->transactionable()->associate($bank);
                    $bankChargesTransaction->account_balance = $bank->total_balance - Request::get('bank_charges');

                    // Update bank balance for bank charges
                    $bank->total_balance = $bank->total_balance - Request::get('bank_charges');
                    $bank->save();

                    $bankChargesTransaction->save();
                }
            });

            return Redirect::back()->with('success', 'Transaction created successfully.');
        } catch (\Exception $e) {
            Log::error('Error in storing transaction: ' . $e->getMessage());

            return Redirect::back()->with('error', 'An error occurred while creating the transaction.');
        }
    }

    private function createTransaction(
        CurrencyOrder $currencyOrder,
        TransactionType $transactionType,
        ?CurrencyOrder $transactionableCurrencyOrder = null
    ): Transaction {
        $transaction = new Transaction;
        $amount = Request::get('amount');

        // For bank charges, always set as credit (deduction)
        if ($transactionType->value === 'bank_charges') {
            $transaction->credit = $amount;
        } else {
            $this->setTransactionAmount($transaction, $currencyOrder, $amount);
        }

        if (($transactionType->value === 'account' || $transactionType->value === 'bank_charges')
            && Request::filled('bank_id')
        ) {
            $bank = Bank::findOrFail(Request::get('bank_id'));
            $currentBalance = $bank->total_balance;

            if ($transaction->debit) {
                $transaction->account_balance = $currentBalance + $transaction->debit;
            } elseif ($transaction->credit) {
                $transaction->account_balance = $currentBalance - $transaction->credit;
            }
        }

        // Set transaction status to completed
        $completedStatus = \App\Models\TransactionStatus::where('value', 'completed')->firstOrFail();
        $transaction->transaction_status_id = $completedStatus->id;

        $transaction->currencyOrder()->associate($currencyOrder->id);
        $transaction->currency()->associate(Request::get('currency_id'));
        $transaction->transactionType()->associate($transactionType);
        $transaction->createdBy()->associate(Auth::user()->id);

        if ($transactionableCurrencyOrder) {
            $transaction->transactionable()->associate($transactionableCurrencyOrder);
        } elseif ($transactionType->value === 'contra' && Request::filled('currency_order_id')) {
            $transaction->transactionable()->associate(CurrencyOrder::findOrFail(Request::get('currency_order_id')));
        } elseif (($transactionType->value === 'account' || $transactionType->value === 'bank_charges')
            && Request::filled('bank_id')
        ) {
            $transaction->transactionable()->associate(Bank::findOrFail(Request::get('bank_id')));
        }

        $transaction->save();

        return $transaction;
    }

    private function setTransactionAmount(Transaction $transaction, CurrencyOrder $currencyOrder, float $amount): void
    {
        $orderType = $currencyOrder->currencyOrderType->value;

        if (in_array($orderType, ['e', 'tpp'])) {
            $transaction->credit = $amount;
        } elseif (in_array($orderType, ['r', 'tpr', 'com'])) {
            $transaction->debit = $amount;
        } elseif ($orderType === 'po') {
            if (Request::get('currency_id') == $currencyOrder->in_currency_id) {
                $transaction->debit = $amount;
            } elseif (Request::get('currency_id') == $currencyOrder->out_currency_id) {
                $transaction->credit = $amount;
            } else {
                throw new \Exception('Transaction currency does not match either in_currency_id or out_currency_id');
            }
        } else {
            throw new \Exception('Invalid Currency Order Type.');
        }
    }

    public function export()
    {
        $filters = request()->only([
            'start_date',
            'end_date',
            'reference',
            'transaction_type',
            'currency',
            'bank',
            'customer',
            'created_by',
        ]);

        // Check if date range was provided
        $autoLimited = false;
        if (empty($filters['start_date']) && empty($filters['end_date'])) {
            $filters['start_date'] = now()->subMonths(3)->format('Y-m-d');
            $filters['end_date'] = now()->format('Y-m-d');
            $autoLimited = true;
        }

        $filename = 'Transactions-' . now()->format('Y-m-d') . '.csv';

        $response = Excel::download(
            new TransactionsExport($filters),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );

        // Add flash message if auto-limited
        if ($autoLimited) {
            session()->flash('info', 'Transaction export limited to last 3 months (' . 
                now()->subMonths(3)->format('M d, Y') . ' to ' . 
                now()->format('M d, Y') . '). Use date filters for custom range.');
        }

        return $response;
    }

    public function exportPdf()
    {
        try {
            $filters = request()->only([
                'start_date',
                'end_date',
                'reference',
                'transaction_type',
                'currency',
                'bank',
                'customer',
                'created_by',
            ]);

            // Check if date range was provided
            $autoLimited = false;
            if (empty($filters['start_date']) && empty($filters['end_date'])) {
                $filters['start_date'] = now()->subMonths(3)->format('Y-m-d');
                $filters['end_date'] = now()->format('Y-m-d');
                $autoLimited = true;
            }

            $query = Transaction::query()
                ->with(['currency', 'transactionType', 'currencyOrder.customer', 'createdBy', 'transactionable', 'transactionStatus'])
                ->when($filters['start_date'] ?? null, function ($query, $date) {
                    $query->whereDate('created_at', '>=', $date);
                })
                ->when($filters['end_date'] ?? null, function ($query, $date) {
                    $query->whereDate('created_at', '<=', $date);
                })
                ->when($filters['reference'] ?? null, function ($query, $reference) {
                    $query->whereHas('currencyOrder', function ($q) use ($reference) {
                        $q->where('reference', 'like', "%{$reference}%");
                    });
                })
                ->when($filters['transaction_type'] ?? null, function ($query, $type) {
                    $query->whereHas('transactionType', function ($q) use ($type) {
                        $q->where('value', $type);
                    });
                })
                ->when($filters['currency'] ?? null, function ($query, $currency) {
                    $query->whereHas('currency', function ($q) use ($currency) {
                        $q->where('code', 'like', "%{$currency}%")
                            ->orWhere('name', 'like', "%{$currency}%");
                    });
                })
                ->when($filters['bank'] ?? null, function ($query, $bank) {
                    $query->whereHasMorph('transactionable', [Bank::class], function ($q) use ($bank) {
                        $q->where('name', 'like', "%{$bank}%");
                    });
                })
                ->when($filters['customer'] ?? null, function ($query, $customer) {
                    $query->whereHas('currencyOrder.customer', function ($q) use ($customer) {
                        $q->where('name', 'like', "%{$customer}%");
                    });
                })
                ->when($filters['created_by'] ?? null, function ($query, $createdBy) {
                    $query->whereHas('createdBy', function ($q) use ($createdBy) {
                        $q->where('name', 'like', "%{$createdBy}%");
                    });
                })
                ->orderBy('created_at', 'desc')
                ->orderBy('transaction_type_id', 'desc')
                ->limit(1000); // Limit for PDF performance

            $transactions = $query->get();

            // Add flash message if auto-limited
            if ($autoLimited) {
                session()->flash('info', 'Transaction export limited to last 3 months (' . 
                    now()->subMonths(3)->format('M d, Y') . ' to ' . 
                    now()->format('M d, Y') . '). Use date filters for custom range.');
            }

            $pdf = PDF::loadView('exports.transactions', [
                'transactions' => $transactions,
                'startDate' => $filters['start_date'] ?? null,
                'endDate' => $filters['end_date'] ?? null,
            ]);

            // Set PDF options for better performance
            $pdf->setPaper('A4', 'landscape');
            
            return $pdf->download('Transactions-' . now()->format('Y-m-d') . '.pdf');
            
        } catch (\Exception $e) {
            \Log::error('PDF Export Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'filters' => $filters ?? []
            ]);
            
            return back()->with('error', 'PDF export failed: ' . $e->getMessage());
        }
    }

    public function show(Transaction $transaction)
    {
        return inertia('Transactions/Show', [
            'transaction' => [
                'id' => $transaction->id,
                'reference' => $transaction->reference ?? ($transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable
                    ? $transaction->transactionable->reference
                    : ($transaction->currencyOrder ? $transaction->currencyOrder->reference : '')),
                'transaction_type' => $transaction->transactionType->name,
                'currency' => [
                    'code' => $transaction->currency->code,
                    'name' => $transaction->currency->name,
                    'photo' => $transaction->currency->photo,
                ],
                'debit' => $transaction->debit,
                'credit' => $transaction->credit,
                'bank' => $transaction->transactionable_type === 'App\Models\Bank' && $transaction->transactionable
                    ? $transaction->transactionable->name
                    : null,
                'account_balance' => $transaction->account_balance,
                'customer' => $transaction->currencyOrder ? $transaction->currencyOrder->customer->name : 'Internal Transfer',
                'created_by' => $transaction->createdBy->name,
                'created_at' => $transaction->created_at,
                'transaction_status' => $transaction->transactionStatus->name,
            ],
            'can' => [
                'view_audit' => auth()->user()->can('view audit') ||
                    auth()->user()->can('transaction audit') ||
                    auth()->user()->can('audit details'),
            ],
        ]);
    }

    public function transactionAudits(Transaction $transaction)
    {
        if (method_exists($transaction, 'audits')) {
            $sort = request()->input('sort', 'created_at');
            $direction = request()->input('direction', 'desc');
            $perPage = request()->input('perPage', 10);

            $audits = $transaction->audits()->with('user')
                ->orderBy($sort, $direction)
                ->paginate($perPage);

            return response()->json([
                'data' => $audits->map(function ($audit) {
                    return [
                        'id' => $audit->id,
                        'user' => $audit->user?->name ?? 'System',
                        'event' => $audit->event,
                        'old_values' => $audit->old_values,
                        'new_values' => $audit->new_values,
                        'created_at' => $audit->created_at,
                    ];
                }),
                'meta' => [
                    'current_page' => $audits->currentPage(),
                    'from' => $audits->firstItem(),
                    'to' => $audits->lastItem(),
                    'total' => $audits->total(),
                    'last_page' => $audits->lastPage(),
                    'per_page' => $audits->perPage(),
                ],
                'links' => [
                    'prev' => $audits->previousPageUrl(),
                    'next' => $audits->nextPageUrl(),
                ],
            ]);
        }

        return response()->json(['data' => []]);
    }

    public function internalTransfer(): RedirectResponse
    {
        Request::validate([
            'from_bank_id' => ['required', 'exists:banks,id'],
            'to_bank_id' => ['required', 'exists:banks,id', 'different:from_bank_id'],
            'amount' => ['required', 'numeric', 'gt:0'],
            'bank_charge' => ['nullable', 'numeric', 'gte:0'],
            'currency_id' => ['required', 'exists:currencies,id'],
        ]);

        try {
            DB::transaction(function () {
                // Get the banks
                $fromBank = Bank::findOrFail(Request::get('from_bank_id'));
                $toBank = Bank::findOrFail(Request::get('to_bank_id'));
                $amount = Request::get('amount');
                $bankCharge = Request::get('bank_charge') ?? 0;
                $currencyId = Request::get('currency_id');

                // Verify both banks use the same currency and are active
                if ($fromBank->currency_id != $currencyId || $toBank->currency_id != $currencyId) {
                    throw new \Exception('Both banks must use the same currency for internal transfer.');
                }

                if (! $fromBank->is_active || ! $toBank->is_active) {
                    throw new \Exception('Both banks must be active for internal transfer.');
                }

                // Verify from bank has sufficient balance
                if ($fromBank->total_balance < ($amount + $bankCharge)) {
                    throw new \Exception('Insufficient balance in the source bank.');
                }

                // Create bank transaction record
                $bankTransaction = new \App\Models\BankTransaction;
                $bankTransaction->from_bank_id = $fromBank->id;
                $bankTransaction->to_bank_id = $toBank->id;
                $bankTransaction->amount = $amount;
                $bankTransaction->bank_charge = $bankCharge > 0 ? $bankCharge : null;
                $bankTransaction->save();

                // Get internal transfer transaction type
                $internalTransferType = TransactionType::where('value', 'internal_transfer')->firstOrFail();
                $completedStatus = \App\Models\TransactionStatus::where('value', 'completed')->firstOrFail();

                // Create debit transaction for source bank
                $debitTransaction = new Transaction;
                $debitTransaction->credit = $amount;
                $debitTransaction->currency_id = $currencyId;
                $debitTransaction->transaction_type_id = $internalTransferType->id;
                $debitTransaction->transaction_status_id = $completedStatus->id;
                $debitTransaction->createdBy()->associate(Auth::user()->id);
                $debitTransaction->transactionable_type = Bank::class;
                $debitTransaction->transactionable_id = $fromBank->id;
                $debitTransaction->account_balance = $fromBank->total_balance - $amount;
                $debitTransaction->bank_transaction_id = $bankTransaction->id;
                $debitTransaction->save();

                // Create credit transaction for destination bank
                $creditTransaction = new Transaction;
                $creditTransaction->debit = $amount;
                $creditTransaction->currency_id = $currencyId;
                $creditTransaction->transaction_type_id = $internalTransferType->id;
                $creditTransaction->transaction_status_id = $completedStatus->id;
                $creditTransaction->createdBy()->associate(Auth::user()->id);
                $creditTransaction->transactionable_type = Bank::class;
                $creditTransaction->transactionable_id = $toBank->id;
                $creditTransaction->account_balance = $toBank->total_balance + $amount;
                $creditTransaction->bank_transaction_id = $bankTransaction->id;
                $creditTransaction->save();

                // Update bank balances for the transfer amount
                $fromBank->total_balance -= $amount;
                $toBank->total_balance += $amount;

                // If there's a bank charge, create a separate transaction with bank_charges type
                if ($bankCharge > 0) {
                    $bankChargesType = TransactionType::where('value', 'bank_charges')->firstOrFail();

                    $bankChargesTransaction = new Transaction;
                    $bankChargesTransaction->credit = $bankCharge;
                    $bankChargesTransaction->currency_id = $currencyId;
                    $bankChargesTransaction->transaction_type_id = $bankChargesType->id;
                    $bankChargesTransaction->transaction_status_id = $completedStatus->id;
                    $bankChargesTransaction->createdBy()->associate(Auth::user()->id);
                    $bankChargesTransaction->transactionable_type = Bank::class;
                    $bankChargesTransaction->transactionable_id = $fromBank->id;
                    $bankChargesTransaction->account_balance = $fromBank->total_balance - $bankCharge;
                    $bankChargesTransaction->bank_transaction_id = $bankTransaction->id;
                    $bankChargesTransaction->save();

                    // Update from bank balance for the bank charge (separately)
                    $fromBank->total_balance -= $bankCharge;
                }

                // Save bank balances
                $fromBank->save();
                $toBank->save();
            });

            return Redirect::back()->with('success', 'Internal bank transfer completed successfully.');
        } catch (\Exception $e) {
            Log::error('Error in internal bank transfer: ' . $e->getMessage());

            return Redirect::back()->with('error', 'An error occurred during the internal bank transfer: ' . $e->getMessage());
        }
    }

    public function cancel(Transaction $transaction)
    {
        try {
            DB::transaction(function () use ($transaction) {
                if ($transaction->transactionStatus->value === 'cancelled') {
                    throw new \Exception('Transaction is already cancelled.');
                }

                $cancelledStatus = \App\Models\TransactionStatus::where('value', 'cancelled')->first();
                if (!$cancelledStatus) {
                    throw new \Exception('Cancelled status not found.');
                }

                $transaction->transaction_status_id = $cancelledStatus->id;
                $transaction->cancelled_by = Auth::id();
                $transaction->cancelled_at = now();
                $transaction->save();

                if ($transaction->currencyOrder) {
                    $this->currencyOrderService->revertTransaction($transaction->currencyOrder, $transaction);
                }
            });

            return redirect()->back()->with('success', 'Transaction cancelled successfully.');
        } catch (\Exception $e) {
            Log::error('Error cancelling transaction: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to cancel transaction: ' . $e->getMessage());
        }
    }
}
